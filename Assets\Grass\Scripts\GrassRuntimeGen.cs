using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using System.Collections.Generic;
using UnityEngine;

// add this script to the same object that holds the grasscomputescript. 
// you can still use the grass window tool to adjust settings
public class GrassRuntimeGen : MonoBehaviour
{
    [SerializeField]
    SO_GrassToolSettings toolSettings;

    [SerializeField]
    GrassComputeScript grassComputeScript;

    NativeArray<float> sizes;
    NativeArray<float> cumulativeSizes;
    NativeArray<float> total;
    public GameObject[] ObjectsArray;
    public int cycles = 1;

    void Awake()
    {
        Generate();
    }

    void Generate()
    {
        GenerateFromArray(ObjectsArray);
    }

    public void GenerateFromArray(GameObject[] arrayToUse)
    {
        ClearGrass();
        for (int i = 0; i < cycles; i++)
        {
            for (int j = 0; j < arrayToUse.Length; j++)
            {
                GeneratePositions(arrayToUse[j]);
            }
        }
    }
    [ContextMenu("Clear Preview")]
    void ClearGrass()
    {
        grassComputeScript.SetGrassPaintedDataList = new List<GrassData>();
        grassComputeScript.Reset();
    }

    [ContextMenu("Preview Gen")]
    void PreviewGeneration()
    {
        Generate();
    }

    public void GeneratePositions(GameObject selection)
    {

        // mesh
        if (selection.TryGetComponent(out MeshFilter sourceMesh))
        {

            CalcAreas(sourceMesh.sharedMesh);
            Matrix4x4 localToWorld = sourceMesh.transform.localToWorldMatrix;

            var oTriangles = sourceMesh.sharedMesh.triangles;
            var oVertices = sourceMesh.sharedMesh.vertices;
            var oColors = sourceMesh.sharedMesh.colors;
            var oNormals = sourceMesh.sharedMesh.normals;

            var meshTriangles = new NativeArray<int>(oTriangles.Length, Allocator.Temp);
            var meshVertices = new NativeArray<Vector4>(oVertices.Length, Allocator.Temp);
            var meshColors = new NativeArray<Color>(oVertices.Length, Allocator.Temp);
            var meshNormals = new NativeArray<Vector3>(oNormals.Length, Allocator.Temp);
            for (int i = 0; i < meshTriangles.Length; i++)
            {
                meshTriangles[i] = oTriangles[i];
            }

            for (int i = 0; i < meshVertices.Length; i++)
            {
                meshVertices[i] = oVertices[i];
                meshNormals[i] = oNormals[i];
                if (oColors.Length == 0)
                {
                    meshColors[i] = Color.black;
                }
                else
                {
                    meshColors[i] = oColors[i];
                }

            }

            var point = new NativeArray<Vector3>(1, Allocator.Temp);

            var normals = new NativeArray<Vector3>(1, Allocator.Temp);

            var lengthWidth = new NativeArray<float>(1, Allocator.Temp);
            var job = new MyJob
            {
                CumulativeSizes = cumulativeSizes,
                MeshColors = meshColors,
                MeshTriangles = meshTriangles,
                MeshVertices = meshVertices,
                MeshNormals = meshNormals,
                Total = total,
                Sizes = sizes,
                Point = point,
                Normals = normals,
                VertexColorSettings = toolSettings.VertexColorSettings,
                VertexFade = toolSettings.VertexFade,
                LengthWidth = lengthWidth,
            };


            Bounds bounds = sourceMesh.sharedMesh.bounds;

            Vector3 meshSize = new Vector3(
                bounds.size.x * sourceMesh.transform.lossyScale.x,
                bounds.size.y * sourceMesh.transform.lossyScale.y,
                bounds.size.z * sourceMesh.transform.lossyScale.z
            );
            meshSize += Vector3.one;

            float meshVolume = meshSize.x * meshSize.y * meshSize.z;
            int numPoints = Mathf.Min(Mathf.FloorToInt(meshVolume * toolSettings.generationDensity), toolSettings.grassAmountToGenerate);


            for (int j = 0; j < numPoints; j++)
            {
                job.Execute();
                GrassData newData = new();
                Vector3 newPoint = point[0];
                newData.position = localToWorld.MultiplyPoint3x4(newPoint);

                Collider[] cols = Physics.OverlapBox(newData.position, Vector3.one * 0.2f, Quaternion.identity, toolSettings.paintBlockMask);
                if (cols.Length > 0)
                {
                    newPoint = Vector3.zero;
                }
                // check normal limit

                Vector3 worldNormal = selection.transform.TransformDirection(normals[0]);

                if (worldNormal.y <= (1 + toolSettings.normalLimit) && worldNormal.y >= (1 - toolSettings.normalLimit))
                {

                    if (newPoint != Vector3.zero)
                    {
                        newData.color = GetRandomColor();
                        newData.length = new Vector2(toolSettings.sizeWidth, toolSettings.sizeLength) * lengthWidth[0];
                        newData.normal = worldNormal;
                        grassComputeScript.SetGrassPaintedDataList.Add(newData);
                    }
                }



            }

            sizes.Dispose();
            cumulativeSizes.Dispose();
            total.Dispose();
            meshColors.Dispose();
            meshTriangles.Dispose();
            meshVertices.Dispose();
            meshNormals.Dispose();
            point.Dispose();
            lengthWidth.Dispose();

            RebuildMesh();
        }

        else if (selection.TryGetComponent(out Terrain terrain))
        {
            // terrainmesh



            float meshVolume = terrain.terrainData.size.x * terrain.terrainData.size.y * terrain.terrainData.size.z;
            int numPoints = Mathf.Min(Mathf.FloorToInt(meshVolume * toolSettings.generationDensity), toolSettings.grassAmountToGenerate);


            for (int j = 0; j < numPoints; j++)
            {
                Matrix4x4 localToWorld = terrain.transform.localToWorldMatrix;
                GrassData newData = new();
                Vector3 newPoint = Vector3.zero;
                Vector3 newNormal = Vector3.zero;
                float[,,] maps = new float[0, 0, 0];
                GetRandomPointOnTerrain(localToWorld, ref maps, terrain, terrain.terrainData.size, ref newPoint, ref newNormal);
                newData.position = newPoint;

                Collider[] cols = Physics.OverlapBox(newData.position, Vector3.one * 0.2f, Quaternion.identity, toolSettings.paintBlockMask);
                if (cols.Length > 0)
                {
                    newPoint = Vector3.zero;
                }


                float getFadeMap = 0;
                // check map layers
                for (int i = 0; i < maps.Length; i++)
                {
                    getFadeMap += System.Convert.ToInt32(toolSettings.layerFading[i]) * maps[0, 0, i];
                    if (maps[0, 0, i] > toolSettings.layerBlocking[i])
                    {
                        newPoint = Vector3.zero;
                    }
                }

                if (newNormal.y <= (1 + toolSettings.normalLimit) && newNormal.y >= (1 - toolSettings.normalLimit))
                {
                    float fade = Mathf.Clamp((getFadeMap), 0, 1f);
                    newData.color = GetRandomColor();
                    newData.length = new Vector2(toolSettings.sizeWidth, toolSettings.sizeLength * fade);
                    newData.normal = newNormal;
                    if (newPoint != Vector3.zero)
                    {
                        grassComputeScript.SetGrassPaintedDataList.Add(newData);
                    }
                }


            }
            RebuildMesh();
        }

    }

    void RebuildMesh()
    {

        grassComputeScript.Reset();


    }

    Vector3 GetRandomColor()
    {
        Color newRandomCol = new(toolSettings.AdjustedColor.r + (Random.Range(0, 1.0f) * toolSettings.rangeR), toolSettings.AdjustedColor.g + (Random.Range(0, 1.0f) * toolSettings.rangeG), toolSettings.AdjustedColor.b + (Random.Range(0, 1.0f) * toolSettings.rangeB), 1);
        Vector3 color = new(newRandomCol.r, newRandomCol.g, newRandomCol.b);
        return color;
    }

    void GetRandomPointOnTerrain(Matrix4x4 localToWorld, ref float[,,] maps, Terrain terrain, Vector3 size, ref Vector3 point, ref Vector3 normal)
    {
        point = new Vector3(Random.Range(0, size.x), 0, Random.Range(0, size.z));
        // sample layers wip

        float pointSizeX = (point.x / size.x);
        float pointSizeZ = (point.z / size.z);

        Vector3 newScale2 = new(pointSizeX * terrain.terrainData.alphamapResolution, 0, pointSizeZ * terrain.terrainData.alphamapResolution);
        int terrainx = Mathf.RoundToInt(newScale2.x);
        int terrainz = Mathf.RoundToInt(newScale2.z);

        maps = terrain.terrainData.GetAlphamaps(terrainx, terrainz, 1, 1);
        normal = terrain.terrainData.GetInterpolatedNormal(pointSizeX, pointSizeZ);
        point = localToWorld.MultiplyPoint3x4(point);
        point.y = terrain.SampleHeight(point) + terrain.GetPosition().y;
    }


    public void CalcAreas(Mesh mesh)
    {
        sizes = GetTriSizes(mesh.triangles, mesh.vertices);
        cumulativeSizes = new NativeArray<float>(sizes.Length, Allocator.Temp);
        total = new NativeArray<float>(1, Allocator.Temp);

        for (int i = 0; i < sizes.Length; i++)
        {
            total[0] += sizes[i];
            cumulativeSizes[i] = total[0];
        }
    }

    // Using BurstCompile to compile a Job with burst
    // Set CompileSynchronously to true to make sure that the method will not be compiled asynchronously
    // but on the first schedule
    [BurstCompile(CompileSynchronously = true)]
    private struct MyJob : IJob
    {
        [ReadOnly]
        public NativeArray<float> Sizes;

        [ReadOnly]
        public NativeArray<float> Total;

        [ReadOnly]
        public NativeArray<float> CumulativeSizes;

        [ReadOnly]
        public NativeArray<Color> MeshColors;

        [ReadOnly]
        public NativeArray<Vector4> MeshVertices;

        [ReadOnly]
        public NativeArray<Vector3> MeshNormals;

        [ReadOnly]
        public NativeArray<int> MeshTriangles;

        [WriteOnly]
        public NativeArray<Vector3> Point;

        [WriteOnly]
        public NativeArray<float> LengthWidth;

        [WriteOnly]
        public NativeArray<Vector3> Normals;

        public SO_GrassToolSettings.VertexColorSetting VertexColorSettings;


        public SO_GrassToolSettings.VertexColorSetting VertexFade;

        public void Execute()
        {
            float randomsample = Random.value * Total[0];
            int triIndex = -1;

            for (int i = 0; i < Sizes.Length; i++)
            {
                if (randomsample <= CumulativeSizes[i])
                {
                    triIndex = i;
                    break;
                }
            }
            if (triIndex == -1)
                Debug.LogError("triIndex should never be -1");

            switch (VertexColorSettings)
            {
                case SO_GrassToolSettings.VertexColorSetting.Red:
                    if (MeshColors[MeshTriangles[triIndex * 3]].r > 0.5f)
                    {
                        Point[0] = Vector3.zero;
                        return;
                    }
                    break;
                case SO_GrassToolSettings.VertexColorSetting.Green:
                    if (MeshColors[MeshTriangles[triIndex * 3]].g > 0.5f)
                    {
                        Point[0] = Vector3.zero;
                        return;
                    }
                    break;
                case SO_GrassToolSettings.VertexColorSetting.Blue:
                    if (MeshColors[MeshTriangles[triIndex * 3]].b > 0.5f)
                    {
                        Point[0] = Vector3.zero;
                        return;
                    }
                    break;
            }

            switch (VertexFade)
            {
                case SO_GrassToolSettings.VertexColorSetting.Red:
                    float red = MeshColors[MeshTriangles[triIndex * 3]].r;
                    float red2 = MeshColors[MeshTriangles[triIndex * 3 + 1]].r;
                    float red3 = MeshColors[MeshTriangles[triIndex * 3 + 2]].r;

                    LengthWidth[0] = 1.0f - ((red + red2 + red3) * 0.3f);
                    break;
                case SO_GrassToolSettings.VertexColorSetting.Green:
                    float green = MeshColors[MeshTriangles[triIndex * 3]].g;
                    float green2 = MeshColors[MeshTriangles[triIndex * 3 + 1]].g;
                    float green3 = MeshColors[MeshTriangles[triIndex * 3 + 2]].g;

                    LengthWidth[0] = 1.0f - ((green + green2 + green3) * 0.3f);
                    break;
                case SO_GrassToolSettings.VertexColorSetting.Blue:
                    float blue = MeshColors[MeshTriangles[triIndex * 3]].b;
                    float blue2 = MeshColors[MeshTriangles[triIndex * 3 + 1]].b;
                    float blue3 = MeshColors[MeshTriangles[triIndex * 3 + 2]].b;

                    LengthWidth[0] = 1.0f - ((blue + blue2 + blue3) * 0.3f);
                    break;
                case SO_GrassToolSettings.VertexColorSetting.None:
                    LengthWidth[0] = 1.0f;
                    break;
            }

            Vector3 a = MeshVertices[MeshTriangles[triIndex * 3]];
            Vector3 b = MeshVertices[MeshTriangles[triIndex * 3 + 1]];
            Vector3 c = MeshVertices[MeshTriangles[triIndex * 3 + 2]];

            // Generate random barycentric coordinates
            float r = Random.value;
            float s = Random.value;

            if (r + s >= 1)
            {
                r = 1 - r;
                s = 1 - s;
            }

            Normals[0] = MeshNormals[MeshTriangles[triIndex * 3 + 1]];

            // Turn point back to a Vector3
            Vector3 pointOnMesh = a + r * (b - a) + s * (c - a);

            Point[0] = pointOnMesh;

        }
    }

    public NativeArray<float> GetTriSizes(int[] tris, Vector3[] verts)
    {
        int triCount = tris.Length / 3;
        var sizes = new NativeArray<float>(triCount, Allocator.Temp);
        for (int i = 0; i < triCount; i++)
        {
            sizes[i] = .5f * Vector3.Cross(
                verts[tris[i * 3 + 1]] - verts[tris[i * 3]],
                verts[tris[i * 3 + 2]] - verts[tris[i * 3]]).magnitude;
        }
        return sizes;
    }
}
