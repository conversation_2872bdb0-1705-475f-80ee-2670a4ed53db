// @Minionsart version
// credits  to  forkercat https://gist.github.com/junhaowww/fb6c030c17fe1e109a34f1c92571943f
// and  NedMakesGames https://gist.github.com/NedMakesGames/3e67fabe49e2e3363a657ef8a6a09838
// for the base setup for compute shaders

// Each #kernel tells which function to compile; you can have many kernels
#pragma kernel Main
// Define some constants
#define TWO_PI      6.28318530717958647693

// This describes a vertex on the source mesh
struct SourceVertex
{
    float3 positionWS; // position in world space
    float3 normalOS;
    float2 uv;  // contains widthMultiplier, heightMultiplier
    float3 color;
};

StructuredBuffer<SourceVertex> _SourceVertices;
StructuredBuffer<int> _VisibleIDBuffer;

StructuredBuffer<float> _CutBuffer;// added for cutting


// This describes a vertex on the generated mesh
struct DrawVertex
{
    float3 positionWS; // The position in world space
    float2 uv;
};

// A triangle on the generated mesh
struct DrawTriangle
{
    float3 normalOS;  
    float3 diffuseColor;
     float4 extraBuffer;
    DrawVertex vertices[3]; // The three points on the triangle
};

// A buffer containing the generated mesh
AppendStructuredBuffer<DrawTriangle> _DrawTriangles;

// The indirect draw call args, as described in the renderer script
struct IndirectArgs
{
    uint numVerticesPerInstance;
    uint numInstances;
    uint startVertexIndex;
    uint startInstanceIndex;
    uint startLocation;
};

// The kernel will count the number of vertices, so this must be RW enabled
RWStructuredBuffer<IndirectArgs> _IndirectArgsBuffer;

// These values are bounded by limits in C# scripts,
// because in the script we need to specify the buffer size
#define GRASS_BLADES 5
#define GRASS_SEGMENTS 2// segments per blade
#define GRASS_NUM_VERTICES_PER_BLADE (GRASS_SEGMENTS * 2)+ 1

// ----------------------------------------

// Variables set by the renderer
int _NumSourceVertices;
// Time
float _Time;

// Grass
half _GrassHeight;
half _GrassWidth;
float _GrassRandomHeightMin, _GrassRandomHeightMax;

// Wind
half _WindSpeed;
float _WindStrength;

// Interactor
half _InteractorStrength;

// Blade
half _BladeRadius;
float _BladeForward;
float _BladeCurve;
float _BottomWidth;
int _MaxBladesPerVertex;
int _MaxSegmentsPerBlade;
float _MinHeight, _MinWidth;
float _MaxHeight, _MaxWidth;
// Camera
float _MinFadeDist, _MaxFadeDist;

// Uniforms
uniform float4 _PositionsMoving[5];
uniform float _InteractorsLength;
uniform float3 _CameraPositionWS;


float3x3 _LocalToWorld;

// ----------------------------------------
// Helper Functions

float rand(float3 co)
{
    return frac(
    sin(dot(co.xyz, float3(12.9898, 78.233, 53.539))) * 43758.5453);
}

float Unity_RandomRange_float(float2 Seed, float Min, float Max)
{
    float randomno =  frac(sin(dot(Seed, float2(12.9898, 78.233)))*43758.5453);
    return lerp(Min, Max, randomno);
}

// A function to compute an rotation matrix which rotates a point
// by angle radians around the given axis
// By Keijiro Takahashi
float3x3 AngleAxis3x3(float angle, float3 axis)
{
    float c, s;
    sincos(angle, s, c);

    float t = 1 - c;
    float x = axis.x;
    float y = axis.y;
    float z = axis.z;

    return float3x3(
    t * x * x + c, t * x * y - s * z, t * x * z + s * y,
    t * x * y + s * z, t * y * y + c, t * y * z - s * x,
    t * x * z - s * y, t * y * z + s * x, t * z * z + c);
}

// Generate each grass vertex for output triangles
DrawVertex GrassVertex(float3 positionWS, float width, float height,
float offset, float curve, float2 uv, float3x3 rotation)
{
    DrawVertex output = (DrawVertex)0;
    float3 newPosOS = positionWS + mul(rotation, float3(width, height, curve + offset));
    output.positionWS = newPosOS;
    output.uv = uv;
    return output;
}

// ----------------------------------------

// The main kernel
[numthreads(128, 1, 1)]
void Main(uint id : SV_DispatchThreadID)
{
    // Return if every point has been processed
    if ((int)id >= _NumSourceVertices)
    {
        return;
    }
    
    int usableID = _VisibleIDBuffer[id];
    // -1 id means it's culled
    if (usableID == -1)
    {
        return;
    }
    // get the right data at the visible ids
    SourceVertex sv = _SourceVertices[usableID];
    float cut =_CutBuffer[usableID];

    // fading on max distance
    float distanceFromCamera = distance(sv.positionWS, _CameraPositionWS);
    float distanceFade = 1 - saturate((distanceFromCamera - _MinFadeDist) / (_MaxFadeDist - _MinFadeDist));

    // skip if out of fading range too
    if (distanceFade < 0)
    {
        return;
    }

    // Blades & Segments
    int numBladesPerVertex = min(GRASS_BLADES, max(1, _MaxBladesPerVertex));
    int numSegmentsPerBlade = min(GRASS_SEGMENTS, max(1, _MaxSegmentsPerBlade));;
    // -1 is because the top part of the grass only has 1 triangle
    int numTrianglesPerBlade = (numSegmentsPerBlade - 1) * 2 + 1;
    
    // normal
    float3 perpendicularAngle = float3(0, 0, 1);
    float3 faceNormal = sv.normalOS;
  
    if(cut != -1){
        sv.color = float3(0,0,0);
    }
    // Wind
    float3 wind1 = float3(
    sin(_Time.x * _WindSpeed + sv.positionWS.x) + sin(
    _Time.x * _WindSpeed + sv.positionWS.z * 2) + sin(
    _Time.x * _WindSpeed * 0.1 + sv.positionWS.x), 0,
    cos(_Time.x * _WindSpeed + sv.positionWS.x * 2) + cos(
    _Time.x * _WindSpeed + sv.positionWS.z));
    wind1 *= _WindStrength;
    
    // Set grass height and width
    _GrassHeight = sv.uv.y;
    _GrassWidth = sv.uv.x;  // UV.x == width multiplier (set in GrassPainter.cs)
     float randomisedPos = rand(sv.positionWS.xyz);
     // random height offsets
    float randomOffset = Unity_RandomRange_float(sv.positionWS.xz, _GrassRandomHeightMin, _GrassRandomHeightMax);
    _GrassHeight = clamp(_GrassHeight + randomOffset, _MinHeight, _MaxHeight);
    _GrassWidth=  clamp(_GrassWidth, _MinWidth, _MaxWidth);
    _GrassWidth *= saturate(distanceFade);
    _BladeForward *= _GrassHeight;
    
    // loop to create blade per vertex
    for (int j = 0; j < numBladesPerVertex * distanceFade ; j++)
    {
         // vertices arrays
        DrawVertex drawVertices[GRASS_NUM_VERTICES_PER_BLADE];
        // set rotation and radius of the blades
        float3x3 facingRotationMatrix = AngleAxis3x3(
        randomisedPos * TWO_PI + j, sv.normalOS);

        float bladeRadius = j / (float) numBladesPerVertex;
        float offset = (1 - bladeRadius) * _BladeRadius;

         // interactivity/bending away from interactors
        float3 combinedDisp = 0;

        // disable movement on a certain threshold replace 0.5 with what you want your threshold to be:
        if(cut < (sv.positionWS.y + 0.5)){
        // disable movement for all cut grass
        // if(cut == -1){}
           
        float3 offsetWorldPos = sv.positionWS + mul(facingRotationMatrix, float3(0, 0, offset));
            for (int p = 0; p < _InteractorsLength; p++)
            {
            float3 playerToVertex = offsetWorldPos - _PositionsMoving[p].xyz;
            float3 directionFromPlayer = normalize(playerToVertex);
            float distanceFromSphere = abs(length(playerToVertex)) + _PositionsMoving[p].w;

            float3 baseXZOffset = float3(directionFromPlayer.x, 0, directionFromPlayer.z) * distanceFromSphere;

            float3 sphereDisp = (baseXZOffset * _InteractorStrength) - float3(0, distanceFromSphere * 1, 0);

            float3 dis = distance(_PositionsMoving[p].xyz, offsetWorldPos);
            float3 radius = 1 - saturate(dis / _PositionsMoving[p].w);
            // in world radius based on objects interaction radius
            sphereDisp *= radius;
            combinedDisp += sphereDisp; // combine
            }
        }
        // create blade
        for (int i = 0; i < numSegmentsPerBlade; i++)
        {
            // taper width, increase height
            float t = i / (float) numSegmentsPerBlade;
            float segmentHeight = _GrassHeight * t;
            float segmentWidth = _GrassWidth * (1 - t);

            // the first (0) grass segment is thinner
            segmentWidth = i == 0 ? _BottomWidth * segmentWidth : segmentWidth;

            float segmentForward = pow(abs(t), _BladeCurve) * _BladeForward;
            // First grass (0) segment does not get displaced by interactor
            float3 newPos = (i == 0) ? sv.positionWS : sv.positionWS + (combinedDisp * t) + wind1 * t + (faceNormal * _GrassHeight * t);
            // Append First Vertex        
            drawVertices[i * 2] = GrassVertex(newPos, segmentWidth, segmentHeight, offset, segmentForward, float2(0, t), facingRotationMatrix);
            // Append Second Vertex
            drawVertices[i * 2 + 1] = GrassVertex(newPos, -segmentWidth, segmentHeight, offset, segmentForward, float2(1, t), facingRotationMatrix);
        }
        // Append Top Vertex
        float3 topPosOS = sv.positionWS + combinedDisp + wind1 + (faceNormal * _GrassHeight);
        drawVertices[numSegmentsPerBlade * 2] = GrassVertex(topPosOS, 0, _GrassHeight, offset, _BladeForward, float2(0.5, 1), facingRotationMatrix);
          
          // add to indirect arguments buffer with the correct vertexcount       
          InterlockedAdd(_IndirectArgsBuffer[0].numVerticesPerInstance, numTrianglesPerBlade * 3);

        // add to the drawbuffer to be read by the final shader
        for (int k = 0; k < numTrianglesPerBlade; ++k)
        {
            DrawTriangle tri = (DrawTriangle)0;
            tri.normalOS = faceNormal;
            tri.diffuseColor = sv.color;
            tri.extraBuffer = float4(cut, 0,0,0);
            tri.vertices[0] = drawVertices[k];
            tri.vertices[1] = drawVertices[k + 1];
            tri.vertices[2] = drawVertices[k + 2];
            _DrawTriangles.Append(tri);
        }
    }
   
}

