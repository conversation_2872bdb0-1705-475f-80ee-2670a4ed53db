%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c489dfd837945034c98c8ccaa0daba75, type: 3}
  m_Name: Grass Settings
  m_EditorClassIdentifier: 
  shaderToUse: {fileID: 7200000, guid: ba1b94a9474830b4ba296433166f046a, type: 3}
  materialToUse: {fileID: 2100000, guid: dcf17f989f4885f44978d8a6e242370c, type: 2}
  grassRandomHeightMin: 1.14
  grassRandomHeightMax: 5
  bladeRadius: 0.2
  bladeForwardAmount: 0.38
  bladeCurveAmount: 2
  bottomWidth: 0.1
  MinWidth: 0.01
  MinHeight: 0.164
  MaxWidth: 0.2
  MaxHeight: 1
  windSpeed: 2
  windStrength: 0.05
  allowedBladesPerVertex: 4
  allowedSegmentsPerBlade: 4
  affectStrength: 1
  topTint: {r: 0.39382136, g: 1, b: 0, a: 1}
  bottomTint: {r: 0.14905155, g: 0.509434, b: 0.07929869, a: 1}
  drawBounds: 0
  minFadeDistance: 40
  maxDrawDistance: 125
  cullingTreeDepth: 4
  cuttingParticles: {fileID: 0}
  castShadow: 0
