%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  - {fileID: 8}
  - {fileID: 13}
  - {fileID: 15}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 2560
    height: 933
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 0
  controlID: 136
  draggingID: 0
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 450
    width: 950
    height: 536
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames: []
  m_SerializedViewValues: []
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1080, y: 2400}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 1
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 06000000000000000000000012000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -540
    m_HBaseRangeMax: 540
    m_VBaseRangeMin: -1200
    m_VBaseRangeMax: 1200
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 1
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 950
      height: 515
    m_Scale: {x: 0.21458334, y: 0.21458334}
    m_Translation: {x: 475, y: 257.5}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -2213.5923
      y: -1200
      width: 4427.1846
      height: 2400
    m_MinimalGUI: 1
  m_defaultScale: 0.21458334
  m_LastWindowPixelSize: {x: 950, y: 536}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000001000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 4}
  - {fileID: 7}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 951
    height: 933
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 8096, y: 16192}
  vertical: 1
  controlID: 25
  draggingID: 0
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 951
    height: 371
  m_MinSize: {x: 101, y: 126}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 5}
  m_Panes:
  - {fileID: 5}
  - {fileID: 6}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 79
    width: 950
    height: 345
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: Tool Settings
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-179.0,"y":-24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -179, y: -24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-141.0,"y":-24.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -141, y: -24}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 2
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":-24.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: -24, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-transform-toolbar
      index: 0
      contents: '{"m_Layout":2,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-component-tools
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Orientation
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-131.5,"y":-24.79998779296875},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -131.5, y: -24.799988}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Particles
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-231.0,"y":-197.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -231, y: -197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap
      index: 11
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: AINavigationOverlay
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-217.5999755859375,"y":-245.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -217.59998, y: -245}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay
      index: 10
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 1
      id: Overlays/OverlayMenu
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: SceneView/CamerasOverlay
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: c1fe389d7c005894687328f513107e1f
  m_Gizmos: 0
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: -0.0034997463, y: -0.005803466, z: 0.1392425}
    speed: 2
    m_Value: {x: -0.0034997463, y: -0.005803466, z: 0.1392425}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 0
    showFog: 1
    showSkybox: 1
    showFlares: 1
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    yGrid:
      m_Fade:
        m_Target: 1
        speed: 2
        m_Value: 1
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    m_ShowGrid: 1
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: -0.17142671, y: 0.6896639, z: -0.17339168, w: -0.6818473}
    speed: 2
    m_Value: {x: -0.17142639, y: 0.6896626, z: -0.17339136, w: -0.68184596}
  m_Size:
    m_Target: 1.0968869
    speed: 2
    m_Value: 1.0968869
  m_Ortho:
    m_Target: 0
    speed: 2
    m_Value: 0
  m_CameraSettings:
    m_Speed: 1
    m_SpeedNormalized: 0.5
    m_SpeedMin: 0.01
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 1
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: 0, y: -0.7071068, z: -0, w: 0.7071068}
  m_LastSceneViewOrtho: 1
  m_Viewpoint:
    m_SceneView: {fileID: 5}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 1
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: 
    section: 
  m_ViewIsLockedToObject: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12914, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Animator
    m_Image: {fileID: -1673928668082335149, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Animator\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 79
    width: 1263
    height: 857
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ViewTransforms:
    m_KeySerializationHelper:
    - {fileID: 5511082235798805414, guid: 132d0e10fe6ec8948a654dbcc6e35110, type: 2}
    m_ValueSerializationHelper:
    - e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  m_PreviewAnimator: {fileID: 0}
  m_AnimatorController: {fileID: 9100000, guid: 132d0e10fe6ec8948a654dbcc6e35110, type: 2}
  m_BreadCrumbs:
  - m_Target: {fileID: 5511082235798805414, guid: 132d0e10fe6ec8948a654dbcc6e35110, type: 2}
    m_ScrollPosition: {x: 0, y: 0}
  stateMachineGraph: {fileID: 0}
  stateMachineGraphGUI: {fileID: 0}
  blendTreeGraph: {fileID: 0}
  blendTreeGraphGUI: {fileID: 0}
  m_AutoLiveLink: 1
  m_MiniTool: 0
  m_LockTracker:
    m_IsLocked: 0
  m_CurrentEditor: 0
  m_LayerEditor:
    m_SelectedLayerIndex: 0
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 371
    width: 951
    height: 562
  m_MinSize: {x: 201, y: 226}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 2}
  m_Panes:
  - {fileID: 2}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 9}
  - {fileID: 11}
  m_Position:
    serializedVersion: 2
    x: 951
    y: 0
    width: 723
    height: 933
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 8096, y: 16192}
  vertical: 1
  controlID: 76
  draggingID: 0
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 723
    height: 364
  m_MinSize: {x: 202, y: 226}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 10}
  m_Panes:
  - {fileID: 10}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 951
    y: 79
    width: 721
    height: 338
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 80aaffff2eb5ffff46b5ffffaeb6ffff34efffff22fbffff
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 4}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: cee38be6aa035a340bde735720f529ad
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 364
    width: 723
    height: 569
  m_MinSize: {x: 102, y: 126}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 12}
  m_Panes:
  - {fileID: 12}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 951
    y: 443
    width: 721
    height: 543
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1674
    y: 0
    width: 368
    height: 933
  m_MinSize: {x: 232, y: 276}
  m_MaxSize: {x: 10002, y: 10026}
  m_ActualView: {fileID: 14}
  m_Panes:
  - {fileID: 14}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1674
    y: 79
    width: 366
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets/Resources/CS_MowGrass
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 0
  m_StartGridSize: 16
  m_LastFolders: []
  m_LastFoldersGridSize: -1
  m_LastProjectPath: D:\Study\UnityURP-MobileDrawMeshInstancedIndirectExample-master
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: a4fc0000
    m_LastClickedID: 64676
    m_ExpandedIDs: 00000000327c0000347c0000367c0000387c00003a7c00003c7c00003e7c0000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: ffffffff00000000327c0000347c0000367c00003a7c0000347e0000d67e0000da7e0000dc7e0000de7e0000b87f0000ba7f0000f0810000b2920000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 13}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 0
    m_ExpandedInstanceIDs: 
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 13}
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 115
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 2042
    y: 0
    width: 518
    height: 933
  m_MinSize: {x: 276, y: 76}
  m_MaxSize: {x: 4001, y: 4026}
  m_ActualView: {fileID: 16}
  m_Panes:
  - {fileID: 16}
  - {fileID: 17}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2042
    y: 79
    width: 517
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: 293.4
    m_ControlHash: -371814159
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12079, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 390, y: 390}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Lighting
    m_Image: {fileID: -1347227620855488341, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Lighting\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1854
    y: 79
    width: 705
    height: 907
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
